package response

import (
	"encoding/json"
	"net/http"
)

// Response represents a standard API response
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
	Code    int         `json:"code"`
}

// JSON sends a JSON response
func JSON(w http.ResponseWriter, statusCode int, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	return json.NewEncoder(w).Encode(data)
}

// Success sends a successful JSON response
func Success(w http.ResponseWriter, data interface{}) error {
	response := Response{
		Success: true,
		Data:    data,
		Code:    http.StatusOK,
	}
	return JSON(w, http.StatusOK, response)
}

// SuccessWithMessage sends a successful JSON response with message
func SuccessWithMessage(w http.ResponseWriter, data interface{}, message string) error {
	response := Response{
		Success: true,
		Data:    data,
		Message: message,
		Code:    http.StatusOK,
	}
	return JSO<PERSON>(w, http.StatusOK, response)
}

// Error sends an error JSON response
func Error(w http.ResponseWriter, statusCode int, message string) error {
	response := Response{
		Success: false,
		Error:   message,
		Code:    statusCode,
	}
	return JSON(w, statusCode, response)
}

// BadRequest sends a 400 Bad Request response
func BadRequest(w http.ResponseWriter, message string) error {
	return Error(w, http.StatusBadRequest, message)
}

// Unauthorized sends a 401 Unauthorized response
func Unauthorized(w http.ResponseWriter, message string) error {
	return Error(w, http.StatusUnauthorized, message)
}

// Forbidden sends a 403 Forbidden response
func Forbidden(w http.ResponseWriter, message string) error {
	return Error(w, http.StatusForbidden, message)
}

// NotFound sends a 404 Not Found response
func NotFound(w http.ResponseWriter, message string) error {
	return Error(w, http.StatusNotFound, message)
}

// InternalServerError sends a 500 Internal Server Error response
func InternalServerError(w http.ResponseWriter, message string) error {
	return Error(w, http.StatusInternalServerError, message)
}

// Created sends a 201 Created response
func Created(w http.ResponseWriter, data interface{}) error {
	response := Response{
		Success: true,
		Data:    data,
		Code:    http.StatusCreated,
	}
	return JSON(w, http.StatusCreated, response)
}

// NoContent sends a 204 No Content response
func NoContent(w http.ResponseWriter) {
	w.WriteHeader(http.StatusNoContent)
}

// ValidationError sends a validation error response
func ValidationError(w http.ResponseWriter, errors map[string]string) error {
	response := map[string]interface{}{
		"success": false,
		"error":   "Validation Error",
		"message": "Request validation failed",
		"code":    http.StatusBadRequest,
		"details": errors,
	}
	return JSON(w, http.StatusBadRequest, response)
}
