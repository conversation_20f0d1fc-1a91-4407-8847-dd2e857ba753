package router

import "strings"

// Use adds middleware to the route group
func (g *RouteGroup) Use(middleware ...MiddlewareFunc) {
	g.middleware = append(g.middleware, middleware...)
}

// GET adds a GET route to the group
func (g *RouteGroup) GET(path string, handler HandlerFunc) {
	g.add<PERSON>oute("GET", path, handler)
}

// POST adds a POST route to the group
func (g *RouteGroup) POST(path string, handler HandlerFunc) {
	g.addRoute("POST", path, handler)
}

// PUT adds a PUT route to the group
func (g *RouteGroup) PUT(path string, handler HandlerFunc) {
	g.addRoute("PUT", path, handler)
}

// DELETE adds a DELETE route to the group
func (g *RouteGroup) DELETE(path string, handler HandlerFunc) {
	g.addRoute("DELETE", path, handler)
}

// PATCH adds a PATCH route to the group
func (g *RouteGroup) PATCH(path string, handler <PERSON><PERSON><PERSON>unc) {
	g.add<PERSON><PERSON><PERSON>("PATCH", path, handler)
}

// Group creates a sub-group with additional prefix
func (g *RouteGroup) Group(prefix string) *RouteGroup {
	return &RouteGroup{
		prefix:     g.prefix + prefix,
		middleware: append([]MiddlewareFunc{}, g.middleware...),
		router:     g.router,
	}
}

// addRoute adds a route to the group with prefix and group middleware
func (g *RouteGroup) addRoute(method, path string, handler HandlerFunc) {
	fullPath := g.prefix + path
	
	// Apply group middleware to handler
	groupHandler := g.applyGroupMiddleware(handler)
	
	route := Route{
		Method:  method,
		Path:    fullPath,
		Handler: groupHandler,
	}
	
	g.router.routes = append(g.router.routes, route)
}

// applyGroupMiddleware applies group middleware to a handler
func (g *RouteGroup) applyGroupMiddleware(handler HandlerFunc) HandlerFunc {
	// Apply group middleware in reverse order
	for i := len(g.middleware) - 1; i >= 0; i-- {
		handler = g.middleware[i](handler)
	}
	return handler
}

// cleanPath cleans up the path by removing double slashes
func cleanPath(path string) string {
	// Remove double slashes
	for strings.Contains(path, "//") {
		path = strings.ReplaceAll(path, "//", "/")
	}
	
	// Ensure path starts with /
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}
	
	return path
}
