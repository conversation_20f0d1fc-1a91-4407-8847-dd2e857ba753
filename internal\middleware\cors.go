package middleware

import (
	"net/http"
	"strings"

	"goframework/internal/router"
)

// CORSConfig represents CORS configuration
type CORSConfig struct {
	AllowOrigins     []string
	AllowMethods     []string
	AllowHeaders     []string
	ExposeHeaders    []string
	AllowCredentials bool
	MaxAge           int
}

// DefaultCORSConfig returns default CORS configuration
func DefaultCORSConfig() CORSConfig {
	return CORSConfig{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"},
		AllowHeaders: []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders: []string{},
		AllowCredentials: false,
		MaxAge: 86400, // 24 hours
	}
}

// CORS creates a CORS middleware with default configuration
func CORS() router.MiddlewareFunc {
	return CORSWithConfig(DefaultCORSConfig())
}

// CORSWithConfig creates a CORS middleware with custom configuration
func CORSWithConfig(config CORSConfig) router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			origin := c.Request.Header.Get("Origin")
			
			// Check if origin is allowed
			if len(config.AllowOrigins) > 0 {
				allowed := false
				for _, allowedOrigin := range config.AllowOrigins {
					if allowedOrigin == "*" || allowedOrigin == origin {
						allowed = true
						break
					}
				}
				
				if allowed {
					if len(config.AllowOrigins) == 1 && config.AllowOrigins[0] == "*" {
						c.Response.Header().Set("Access-Control-Allow-Origin", "*")
					} else {
						c.Response.Header().Set("Access-Control-Allow-Origin", origin)
					}
				}
			}
			
			// Set other CORS headers
			if len(config.AllowMethods) > 0 {
				c.Response.Header().Set("Access-Control-Allow-Methods", strings.Join(config.AllowMethods, ", "))
			}
			
			if len(config.AllowHeaders) > 0 {
				c.Response.Header().Set("Access-Control-Allow-Headers", strings.Join(config.AllowHeaders, ", "))
			}
			
			if len(config.ExposeHeaders) > 0 {
				c.Response.Header().Set("Access-Control-Expose-Headers", strings.Join(config.ExposeHeaders, ", "))
			}
			
			if config.AllowCredentials {
				c.Response.Header().Set("Access-Control-Allow-Credentials", "true")
			}
			
			if config.MaxAge > 0 {
				c.Response.Header().Set("Access-Control-Max-Age", string(rune(config.MaxAge)))
			}
			
			// Handle preflight request
			if c.Request.Method == "OPTIONS" {
				c.Response.WriteHeader(http.StatusNoContent)
				return nil
			}
			
			return next(c)
		}
	}
}

// CORSAllowAll creates a permissive CORS middleware
func CORSAllowAll() router.MiddlewareFunc {
	config := CORSConfig{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: false,
		MaxAge:           86400,
	}
	return CORSWithConfig(config)
}
