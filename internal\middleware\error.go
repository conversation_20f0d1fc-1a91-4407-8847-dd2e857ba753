package middleware

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"goframework/internal/router"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// ErrorHandler creates an error handling middleware
func ErrorHandler() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			err := next(c)
			if err != nil {
				// Log the error
				log.Printf("Error handling request: %v", err)
				
				// Return error response
				c.Response.Header().Set("Content-Type", "application/json")
				c.Response.WriteHeader(http.StatusInternalServerError)
				
				errorResp := ErrorResponse{
					Error:   "Internal Server Error",
					Message: err.Error(),
					Code:    http.StatusInternalServerError,
				}
				
				json.NewEncoder(c.Response).Encode(errorResp)
			}
			return nil // Always return nil to prevent further error propagation
		}
	}
}

// ErrorHandlerWithConfig creates an error handling middleware with custom configuration
func ErrorHandlerWithConfig(handler func(*router.Context, error)) router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			err := next(c)
			if err != nil {
				handler(c, err)
			}
			return nil
		}
	}
}

// HTTPError represents an HTTP error with status code
type HTTPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Error implements the error interface
func (e HTTPError) Error() string {
	return e.Message
}

// NewHTTPError creates a new HTTP error
func NewHTTPError(code int, message string) HTTPError {
	return HTTPError{
		Code:    code,
		Message: message,
	}
}

// HTTPErrorHandler creates an HTTP error handling middleware
func HTTPErrorHandler() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			err := next(c)
			if err != nil {
				// Check if it's an HTTPError
				if httpErr, ok := err.(HTTPError); ok {
					c.Response.Header().Set("Content-Type", "application/json")
					c.Response.WriteHeader(httpErr.Code)
					
					errorResp := ErrorResponse{
						Error:   http.StatusText(httpErr.Code),
						Message: httpErr.Message,
						Code:    httpErr.Code,
					}
					
					json.NewEncoder(c.Response).Encode(errorResp)
				} else {
					// Generic error
					log.Printf("Error handling request: %v", err)
					
					c.Response.Header().Set("Content-Type", "application/json")
					c.Response.WriteHeader(http.StatusInternalServerError)
					
					errorResp := ErrorResponse{
						Error:   "Internal Server Error",
						Message: err.Error(),
						Code:    http.StatusInternalServerError,
					}
					
					json.NewEncoder(c.Response).Encode(errorResp)
				}
			}
			return nil
		}
	}
}

// ValidationErrorHandler handles validation errors
func ValidationErrorHandler() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			err := next(c)
			if err != nil {
				// Check if it's a validation error (we'll define this later)
				if validationErr, ok := err.(*ValidationError); ok {
					c.Response.Header().Set("Content-Type", "application/json")
					c.Response.WriteHeader(http.StatusBadRequest)
					
					errorResp := map[string]interface{}{
						"error":   "Validation Error",
						"message": "Request validation failed",
						"code":    http.StatusBadRequest,
						"details": validationErr.Errors,
					}
					
					json.NewEncoder(c.Response).Encode(errorResp)
					return nil
				}
			}
			return err
		}
	}
}

// ValidationError represents validation errors
type ValidationError struct {
	Errors map[string]string `json:"errors"`
}

// Error implements the error interface
func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation failed: %v", e.Errors)
}

// NewValidationError creates a new validation error
func NewValidationError() *ValidationError {
	return &ValidationError{
		Errors: make(map[string]string),
	}
}

// AddError adds a field error
func (e *ValidationError) AddError(field, message string) {
	e.Errors[field] = message
}

// HasErrors returns true if there are validation errors
func (e *ValidationError) HasErrors() bool {
	return len(e.Errors) > 0
}
