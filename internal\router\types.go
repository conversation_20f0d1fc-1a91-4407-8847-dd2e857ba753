package router

import (
	"context"
	"net/http"
)

// Context wraps http.Request and http.ResponseWriter with additional functionality
type Context struct {
	Request  *http.Request
	Response http.ResponseWriter
	Params   map[string]string
	ctx      context.Context
}

// NewContext creates a new Context instance
func NewContext(w http.ResponseWriter, r *http.Request) *Context {
	return &Context{
		Request:  r,
		Response: w,
		Params:   make(map[string]string),
		ctx:      r.Context(),
	}
}

// <PERSON>m returns the value of the URL parameter
func (c *Context) Param(key string) string {
	return c.Params[key]
}

// SetParam sets a URL parameter
func (c *Context) SetParam(key, value string) {
	c.Params[key] = value
}

// Context returns the request context
func (c *Context) Context() context.Context {
	return c.ctx
}

// HandlerFunc defines the handler function signature
type HandlerFunc func(*Context) error

// MiddlewareFunc defines the middleware function signature
type MiddlewareFunc func(HandlerFunc) HandlerFunc

// Route represents a single route
type Route struct {
	Method  string
	Path    string
	Handler HandlerFunc
}

// RouteGroup represents a group of routes with common prefix and middleware
type RouteGroup struct {
	prefix      string
	middleware  []MiddlewareFunc
	router      *Router
}

// Router represents the main router
type Router struct {
	routes     []Route
	middleware []MiddlewareFunc
	notFound   HandlerFunc
}
