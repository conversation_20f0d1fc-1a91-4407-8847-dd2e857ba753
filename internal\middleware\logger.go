package middleware

import (
	"fmt"
	"log"
	"time"

	"goframework/internal/router"
)

// Lo<PERSON> creates a logging middleware
func Logger() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			start := time.Now()
			
			// Log request
			log.Printf("Started %s %s", c.Request.Method, c.Request.URL.Path)
			
			// Execute next handler
			err := next(c)
			
			// Log response
			duration := time.Since(start)
			log.Printf("Completed %s %s in %v", c.Request.Method, c.Request.URL.Path, duration)
			
			return err
		}
	}
}

// CustomLogger creates a logging middleware with custom logger
func CustomLogger(logger *log.Logger) router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			start := time.Now()
			
			// Log request
			logger.Printf("Started %s %s", c.Request.Method, c.Request.URL.Path)
			
			// Execute next handler
			err := next(c)
			
			// Log response
			duration := time.Since(start)
			status := "200" // Default status, can be enhanced
			if err != nil {
				status = "500"
			}
			
			logger.Printf("Completed %s %s %s in %v", c.Request.Method, c.Request.URL.Path, status, duration)
			
			return err
		}
	}
}

// RequestLogger logs detailed request information
func RequestLogger() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			start := time.Now()
			
			// Log detailed request info
			log.Printf("Request: %s %s", c.Request.Method, c.Request.URL.String())
			log.Printf("Headers: %v", c.Request.Header)
			log.Printf("Remote Addr: %s", c.Request.RemoteAddr)
			log.Printf("User Agent: %s", c.Request.UserAgent())
			
			// Execute next handler
			err := next(c)
			
			// Log completion
			duration := time.Since(start)
			log.Printf("Request completed in %v", duration)
			
			return err
		}
	}
}

// AccessLogger creates an access log entry
func AccessLogger() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			start := time.Now()
			
			// Execute next handler
			err := next(c)
			
			// Create access log entry
			duration := time.Since(start)
			status := "200"
			if err != nil {
				status = "500"
			}
			
			// Common Log Format
			logEntry := fmt.Sprintf("%s - - [%s] \"%s %s %s\" %s %d %v",
				c.Request.RemoteAddr,
				start.Format("02/Jan/2006:15:04:05 -0700"),
				c.Request.Method,
				c.Request.URL.Path,
				c.Request.Proto,
				status,
				0, // Content length - would need to be tracked
				duration,
			)
			
			log.Println(logEntry)
			
			return err
		}
	}
}
