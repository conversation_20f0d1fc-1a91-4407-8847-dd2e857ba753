package router

import (
	"net/http"
	"strings"
)

// New creates a new Router instance
func New() *Router {
	return &Router{
		routes:     make([]Route, 0),
		middleware: make([]MiddlewareFunc, 0),
		notFound: func(c *Context) error {
			c.Response.WriteHeader(http.StatusNotFound)
			c.Response.Write([]byte("404 Not Found"))
			return nil
		},
	}
}

// Use adds middleware to the router
func (r *Router) Use(middleware ...MiddlewareFunc) {
	r.middleware = append(r.middleware, middleware...)
}

// GET adds a GET route
func (r *Router) GET(path string, handler HandlerFunc) {
	r.addRoute("GET", path, handler)
}

// POST adds a POST route
func (r *Router) POST(path string, handler HandlerFunc) {
	r.addRoute("POST", path, handler)
}

// PUT adds a PUT route
func (r *Router) PUT(path string, handler HandlerFunc) {
	r.addRoute("PUT", path, handler)
}

// DELETE adds a DELETE route
func (r *Router) DELETE(path string, handler HandlerFunc) {
	r.addRoute("DELETE", path, handler)
}

// PATCH adds a PATCH route
func (r *Router) PATCH(path string, handler HandlerFunc) {
	r.addRoute("PATCH", path, handler)
}

// Group creates a new route group with prefix
func (r *Router) Group(prefix string) *RouteGroup {
	return &RouteGroup{
		prefix:     prefix,
		middleware: make([]MiddlewareFunc, 0),
		router:     r,
	}
}

// NotFound sets the 404 handler
func (r *Router) NotFound(handler HandlerFunc) {
	r.notFound = handler
}

// addRoute adds a route to the router
func (r *Router) addRoute(method, path string, handler HandlerFunc) {
	route := Route{
		Method:  method,
		Path:    path,
		Handler: handler,
	}
	r.routes = append(r.routes, route)
}

// ServeHTTP implements http.Handler interface
func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	ctx := NewContext(w, req)
	
	// Find matching route
	route, params := r.findRoute(req.Method, req.URL.Path)
	if route == nil {
		// Apply middleware chain to not found handler
		handler := r.applyMiddleware(r.notFound)
		handler(ctx)
		return
	}
	
	// Set route parameters
	for key, value := range params {
		ctx.SetParam(key, value)
	}
	
	// Apply middleware chain
	handler := r.applyMiddleware(route.Handler)
	if err := handler(ctx); err != nil {
		// Error handling will be done by error middleware
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

// findRoute finds a matching route and extracts parameters
func (r *Router) findRoute(method, path string) (*Route, map[string]string) {
	for _, route := range r.routes {
		if route.Method != method {
			continue
		}
		
		if params, ok := r.matchPath(route.Path, path); ok {
			return &route, params
		}
	}
	return nil, nil
}

// matchPath checks if a route path matches the request path and extracts parameters
func (r *Router) matchPath(routePath, requestPath string) (map[string]string, bool) {
	routeParts := strings.Split(strings.Trim(routePath, "/"), "/")
	requestParts := strings.Split(strings.Trim(requestPath, "/"), "/")
	
	if len(routeParts) != len(requestParts) {
		return nil, false
	}
	
	params := make(map[string]string)
	
	for i, routePart := range routeParts {
		if strings.HasPrefix(routePart, ":") {
			// Parameter
			paramName := routePart[1:]
			params[paramName] = requestParts[i]
		} else if routePart != requestParts[i] {
			// Static part doesn't match
			return nil, false
		}
	}
	
	return params, true
}

// applyMiddleware applies all middleware to a handler
func (r *Router) applyMiddleware(handler HandlerFunc) HandlerFunc {
	// Apply middleware in reverse order
	for i := len(r.middleware) - 1; i >= 0; i-- {
		handler = r.middleware[i](handler)
	}
	return handler
}
