package middleware

import (
	"fmt"
	"log"
	"net/http"
	"runtime"

	"goframework/internal/router"
)

// Recover creates a recovery middleware that catches panics
func Recover() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			defer func() {
				if err := recover(); err != nil {
					// Log the panic
					log.Printf("Panic recovered: %v", err)
					
					// Print stack trace
					buf := make([]byte, 1024)
					for {
						n := runtime.Stack(buf, false)
						if n < len(buf) {
							buf = buf[:n]
							break
						}
						buf = make([]byte, 2*len(buf))
					}
					log.Printf("Stack trace:\n%s", buf)
					
					// Return 500 Internal Server Error
					c.Response.WriteHeader(http.StatusInternalServerError)
					c.Response.Write([]byte("Internal Server Error"))
				}
			}()
			
			return next(c)
		}
	}
}

// RecoverWithHandler creates a recovery middleware with custom error handler
func RecoverWithHandler(handler func(*router.Context, interface{})) router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			defer func() {
				if err := recover(); err != nil {
					// Log the panic
					log.Printf("Panic recovered: %v", err)
					
					// Print stack trace
					buf := make([]byte, 1024)
					for {
						n := runtime.Stack(buf, false)
						if n < len(buf) {
							buf = buf[:n]
							break
						}
						buf = make([]byte, 2*len(buf))
					}
					log.Printf("Stack trace:\n%s", buf)
					
					// Call custom handler
					handler(c, err)
				}
			}()
			
			return next(c)
		}
	}
}

// RecoverJSON creates a recovery middleware that returns JSON error response
func RecoverJSON() router.MiddlewareFunc {
	return func(next router.HandlerFunc) router.HandlerFunc {
		return func(c *router.Context) error {
			defer func() {
				if err := recover(); err != nil {
					// Log the panic
					log.Printf("Panic recovered: %v", err)
					
					// Print stack trace
					buf := make([]byte, 1024)
					for {
						n := runtime.Stack(buf, false)
						if n < len(buf) {
							buf = buf[:n]
							break
						}
						buf = make([]byte, 2*len(buf))
					}
					log.Printf("Stack trace:\n%s", buf)
					
					// Return JSON error response
					c.Response.Header().Set("Content-Type", "application/json")
					c.Response.WriteHeader(http.StatusInternalServerError)
					
					errorResponse := fmt.Sprintf(`{"error": "Internal Server Error", "message": "%v"}`, err)
					c.Response.Write([]byte(errorResponse))
				}
			}()
			
			return next(c)
		}
	}
}
